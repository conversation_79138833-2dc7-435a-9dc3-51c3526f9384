// 注意：NEW_FREE / NEW_PAID 在此库中不存在！可用选项：TOP_FREE / TOP_PAID / GROSSING
// 如需获取新游戏，请参考 new_games_solution.js 文件中的多种方案
import gplay from "google-play-scraper";

// 拉取“游戏-动作”分类中的列表；把 collection 改成 NEW_FREE / NEW_PAID 试试“新品”
const apps = await gplay.list({
  category: gplay.category.GAME,     // 或具体子类：GAME_ACTION、GAME_ARCADE...
  collection: gplay.collection.TOP_FREE, // 可用选项：TOP_FREE / TOP_PAID / GROSSING
  //country: 'jp',                     // 选择市场，结果会因地区不同
  //lang: 'zh',                        // 语言
  num: 100,                          // 返回数量
  throttle: 5                        // 限速，避免被 503/CAPTCHA
});
console.log(apps.map(a => ({title: a.title, appId: a.appId})));