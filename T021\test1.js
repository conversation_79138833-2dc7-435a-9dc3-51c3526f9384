import gplay from "google-play-scraper";

// 拉取“游戏-动作”分类中的列表；把 collection 改成 NEW_FREE / NEW_PAID 试试“新品”
const apps = await gplay.list({
  category: gplay.category.GAME,     // 或具体子类：GAME_ACTION、GAME_ARCADE...
  collection: gplay.collection.NEW_FREE, // NEW_FREE / NEW_PAID / TOP_FREE / TOP_PAID 等
  //country: 'jp',                     // 选择市场，结果会因地区不同
  //lang: 'zh',                        // 语言
  num: 100,                          // 返回数量
  throttle: 5                        // 限速，避免被 503/CAPTCHA
});
console.log(apps.map(a => ({title: a.title, appId: a.appId})));