const gplay = require('google-play-scraper');
const fs = require('fs');

async function scrapeNewGames() {
  try {
    const results = await gplay.list({
      category: gplay.category.GAME_ACTION,
      collection: gplay.collection.NEW_FREE,
      num: 500, // 每次最多抓取 100 条
      lang: 'en',
      country: 'us',
      fullDetail: false // 设置为 true 可获取详细数据，但请求量增加
    });

    // 保存结果到 JSON 文件
    fs.writeFileSync('new_games.json', JSON.stringify(results, null, 2));
    console.log('New games saved to new_games.json');

    // 打印部分结果
    results.forEach((game, index) => {
      console.log(`${index + 1}. ${game.title} by ${game.developer} (${game.scoreText})`);
    });
  } catch (error) {
    console.error('Error fetching new games:', error);
  }
}

scrapeNewGames();