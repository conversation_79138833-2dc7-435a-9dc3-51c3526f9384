import argparse
import csv
import sys
import time
from datetime import datetime, timedelta, timezone
from dateutil import parser as dateparser
from tqdm import tqdm

# google-play-scraper（Python 版）
# 安装名：google-play-scraper
# 导入名：google_play_scraper
from google_play_scraper import app as gp_app
from google_play_scraper import search as gp_search

def safe_parse_date(text: str):
    """
    将 'Aug 20, 2025' / '2025年8月20日' 等本地化日期解析为日期（无时区，返回 UTC 00:00 的 datetime）。
    解析失败返回 None。
    """
    if not text:
        return None
    try:
        dt = dateparser.parse(text)
        # 只取日期部分，统一到当天的 00:00 UTC，便于比较
        return datetime(dt.year, dt.month, dt.day, tzinfo=timezone.utc)
    except Exception:
        return None

def fetch_new_games(country='us', lang='en', num=200, sleep=0.2):
    """
    拉取 Google Play“新品”榜下的游戏（免费 + 付费），返回基本信息列表（含 appId）。
    """
    items = []

    # 新品-免费
    for c in [Collection.NEW_FREE, Collection.NEW_PAID]:
        batch = gp_collection(
            collection=c,
            category=Category.GAME,  # 可换成具体子分类，如 Category.GAME_ACTION
            country=country,
            lang=lang,
            num=num
        )
        for it in batch:
            it["__source_collection"] = c.name
        items.extend(batch)

        # 防止触发风控，轻微 sleep
        time.sleep(sleep)

    # 去重（按 appId）
    seen = set()
    deduped = []
    for it in items:
        if it['appId'] not in seen:
            seen.add(it['appId'])
            deduped.append(it)
    return deduped

def enrich_details(app_ids, country='us', lang='en', sleep=0.25):
    """
    对每个 appId 拉取详情（包含 released / updated / installs / ratings 等）。
    返回详情 dict 列表。
    """
    details = []
    for aid in tqdm(app_ids, desc="Fetching app details"):
        try:
            d = gp_app(aid, country=country, lang=lang)
            details.append(d)
        except Exception as e:
            # 打印但不中断
            print(f"[warn] failed to fetch {aid}: {e}", file=sys.stderr)
        time.sleep(sleep)
    return details

def filter_by_days(details, days=1):
    """
    只保留最近 days 天内“首次发布(released)”或“更新(updated)”的游戏。
    逻辑：
      1) 优先用 released（首发日期）
      2) 若 released 缺失，回退为 updated（最后更新）
    """
    if days is None or days <= 0:
        return details

    # 以用户所在时区为准通常更直观。这里用系统本地时间；若要指定时区可自定义。
    now_local = datetime.now().astimezone()
    window_start_local = now_local - timedelta(days=days)

    kept = []
    for d in details:
        released_raw = d.get('released')
        updated_raw  = d.get('updated')  # 注意：有的版本返回 datetime，有的返回字符串，库版本不同略有差异

        released_dt = None
        if isinstance(released_raw, str):
            released_dt = safe_parse_date(released_raw)
        elif isinstance(released_raw, datetime):
            released_dt = released_raw

        updated_dt = None
        if isinstance(updated_raw, str):
            updated_dt = safe_parse_date(updated_raw)
        elif isinstance(updated_raw, datetime):
            updated_dt = updated_raw

        # 统一比较到本地时区的日期维度
        target_dt = released_dt or updated_dt
        if not target_dt:
            continue

        # 转到本地时区比较
        target_local = target_dt.astimezone(now_local.tzinfo)
        if target_local >= window_start_local:
            kept.append(d)
    return kept

def main():
    parser = argparse.ArgumentParser(description="Fetch newly released Google Play games.")
    parser.add_argument("--country", default="us", help="Country code, e.g., us / jp / de / cn (默认 us)")
    parser.add_argument("--lang", default="en", help="Language code, e.g., en / zh / ja (默认 en)")
    parser.add_argument("--num", type=int, default=200, help="从“新品”榜单拉取的数量上限（每个榜单）")
    parser.add_argument("--days", type=int, default=3, help="仅保留最近 N 天内首发或更新的游戏；0 表示不过滤")
    parser.add_argument("--out", default="new_games.csv", help="导出 CSV 文件名")
    args = parser.parse_args()

    # 1) 拉取新品榜（免费+付费）
    base = fetch_new_games(country=args.country, lang=args.lang, num=args.num)

    # 2) 详情补全
    app_ids = [x["appId"] for x in base]
    details = enrich_details(app_ids, country=args.country, lang=args.lang)

    # 3) 时间窗口过滤
    filtered = filter_by_days(details, days=args.days)

    # 4) 合并基础信息中的 __source_collection（NEW_FREE / NEW_PAID）
    source_map = {x["appId"]: x.get("__source_collection") for x in base}
    for d in filtered:
        d["__source_collection"] = source_map.get(d["appId"])

    # 5) 导出 CSV（挑选常用字段）
    fields = [
        "appId", "title", "developer", "score", "ratings", "installs",
        "released", "updated", "genre", "genreId", "free", "price",
        "__source_collection", "url", "description"
    ]

    with open(args.out, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fields, extrasaction="ignore")
        writer.writeheader()
        for d in filtered:
            writer.writerow(d)

    print(f"Done. Saved {len(filtered)} apps to {args.out}")

if __name__ == "__main__":
    main()
